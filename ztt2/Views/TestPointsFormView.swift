//
//  TestPointsFormView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 测试积分表单弹窗的视图
 * 用于验证修改后的弹窗样式是否与奖品弹窗一致
 */
struct TestPointsFormView: View {
    
    @State private var showAddPointsForm = false
    @State private var showDeductPointsForm = false
    @State private var showAddRewardForm = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("弹窗样式对比测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 40)
                
                VStack(spacing: 20) {
                    // 添加加分规则按钮
                    Button(action: {
                        showAddPointsForm = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(.green)
                            Text("添加加分规则")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(12)
                    }
                    
                    // 添加扣分规则按钮
                    Button(action: {
                        showDeductPointsForm = true
                    }) {
                        HStack {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                            Text("添加扣分规则")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(12)
                    }
                    
                    // 添加奖品按钮（对比参考）
                    Button(action: {
                        showAddRewardForm = true
                    }) {
                        HStack {
                            Image(systemName: "gift.fill")
                                .foregroundColor(.orange)
                            Text("添加奖品（参考样式）")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                Text("点击按钮查看弹窗样式是否一致")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.bottom, 40)
            }
            .navigationBarHidden(true)
        }
        .overlay(
            // 添加加分规则弹窗
            MemberPointsFormView(
                isPresented: $showAddPointsForm,
                operationType: .add,
                isAddingRuleOnly: true,
                onSubmit: { formData in
                    print("添加加分规则: \(formData.items.count) 项")
                    showAddPointsForm = false
                },
                onCancel: {
                    showAddPointsForm = false
                }
            )
        )
        .overlay(
            // 添加扣分规则弹窗
            MemberPointsFormView(
                isPresented: $showDeductPointsForm,
                operationType: .deduct,
                isAddingRuleOnly: true,
                onSubmit: { formData in
                    print("添加扣分规则: \(formData.items.count) 项")
                    showDeductPointsForm = false
                },
                onCancel: {
                    showDeductPointsForm = false
                }
            )
        )
        .overlay(
            // 添加奖品弹窗（参考样式）
            AddRewardFormView(
                isPresented: $showAddRewardForm,
                onSubmit: { formData in
                    print("添加奖品: \(formData.items.count) 项")
                    showAddRewardForm = false
                },
                onCancel: {
                    showAddRewardForm = false
                }
            )
        )
    }
}

// MARK: - Preview
#Preview {
    TestPointsFormView()
}
